ДЕТАЙЛЕН АНАЛИЗ НА ISO 15765-2 ИМПЛЕМЕНТАЦИЯТА
ЛИНИЯ ПО ЛИНИЯ АНАЛИЗ НА TRANSCRIPT.TXT
=====================================================

РЕФЕРЕНЦИЯ КЪМ ISO 15765-2 СТАНДАРТА:
====================================
Според официалната спецификация ISO 15765-2:2016, протоколът дефинира 4 типа рамки:
- Single Frame (SF): PCI = 0x0X (X = дължина на данните 0-7 байта)
- First Frame (FF): PCI = 0x1X (X = горни 4 бита от общата дължина)
- Consecutive Frame (CF): PCI = 0x2X (X = sequence number 1-15, wrap to 0)
- Flow Control (FC): PCI = 0x3X (X = flow status: 0=Continue, 1=Wait, 2=Overflow)

АНАЛИЗ НА ВСЯКА ЛИНИЯ ОТ TRANSCRIPT.TXT:
=======================================

ЛИНИЯ 1: 7400210C00000000000
---------------------------
Сурови hex данни: 7400210C00000000000
CAN ID: 0x7400 (първи 2 байта: 74 00)
PCI байт: 0x21
  - Тип рамка: 0x2 (Consecutive Frame)
  - Sequence number: 0x1
Payload данни: 0C00000000000 (7 байта)
Сглобяване: CF без предхождаща FF рамка - НАРУШЕНИЕ НА ПРОТОКОЛА!
Протоколно нарушение: Consecutive Frame без First Frame
Обработка от парсъра: Започва ново съобщение (нестандартно поведение)
Финален изход: Ще се сглоби с последващи CF рамки

ЛИНИЯ 2: 7600650C0003201F4AA
---------------------------
Сурови hex данни: 7600650C0003201F4AA
CAN ID: 0x7600 (първи 2 байта: 76 00)
PCI байт: 0x65
  - Тип рамка: 0x6 (НЕВАЛИДЕН ТИП!)
  - Според стандарта: само 0-3 са валидни типове
Протоколно нарушение: Невалиден PCI тип 0x6
Обработка от парсъра: Третира като неизвестен тип, копира данните след PCI
Payload данни: 0C0003201F4A (6 байта)
Финален изход: 7600: 0C0003201F4A

ЛИНИЯ 3: 7400221830000000000
---------------------------
Сурови hex данни: 7400221830000000000
CAN ID: 0x7400 (същия като линия 1)
PCI байт: 0x22
  - Тип рамка: 0x2 (Consecutive Frame)
  - Sequence number: 0x2
Payload данни: 1830000000000 (7 байта)
Сглобяване: Продължава съобщението започнато в линия 1
Протоколна валидност: Правилен sequence number (1→2)
Обработка: Добавя данните към частично сглобеното съобщение

ЛИНИЯ 4: 760101A618339484D31
---------------------------
Сурови hex данни: 760101A618339484D31
CAN ID: 0x7601 (първи 2 байта: 76 01)
PCI байт: 0x01
  - Тип рамка: 0x0 (Single Frame)
  - Дължина на данните: 0x1 (1 байт)
Payload данни: A6 (1 байт според PCI)
Сглобяване: Пълно съобщение в една рамка
Протоколна валидност: ✅ ПРАВИЛНО според ISO 15765-2
Финален изход: 7601: A6

ЛИНИЯ 5: 7403000000000000000
---------------------------
Сурови hex данни: 7403000000000000000
CAN ID: 0x7403 (първи 2 байта: 74 03)
PCI байт: 0x00
  - Тип рамка: 0x0 (Single Frame)
  - Дължина на данните: 0x0 (0 байта)
Payload данни: (няма данни)
Сглобяване: Празно съобщение
Протоколна валидност: ✅ ПРАВИЛНО (празни SF са позволени)
Обработка: Не се печата (няма данни)

ЛИНИЯ 6: 7602141341101000265
---------------------------
Сурови hex данни: 7602141341101000265
CAN ID: 0x7602 (първи 2 байта: 76 02)
PCI байт: 0x14
  - Тип рамка: 0x1 (First Frame)
  - Горни 4 бита от дължината: 0x4
  - Следващ байт: 0x13 (19 в десетична)
  - Обща дължина: (0x4 << 8) | 0x13 = 1043 байта
Payload данни: 41101000265 (5 байта)
Сглобяване: Започва ново многорамково съобщение
Протоколна валидност: ✅ ПРАВИЛНО според ISO 15765-2
Очаквани CF рамки: За 1043 байта ще трябват много CF рамки

ЛИНИЯ 7: 7602295616529201203
---------------------------
Сурови hex данни: 7602295616529201203
CAN ID: 0x7602 (същия като линия 6)
PCI байт: 0x29
  - Тип рамка: 0x2 (Consecutive Frame)
  - Sequence number: 0x9
Payload данни: 5616529201203 (7 байта)
Сглобяване: Продължава съобщението от линия 6
Протоколно нарушение: Очакван sequence 1, получен 9 - ПРОПУСНАТИ РАМКИ!
Обработка: Парсърът е "гъвкав" и приема рамката

ЛИНИЯ 8: 76023000000000080AA
---------------------------
Сурови hex данни: 76023000000000080AA
CAN ID: 0x7602 (същия като линии 6-7)
PCI байт: 0x30
  - Тип рамка: 0x3 (Flow Control)
  - Flow status: 0x0 (Continue to Send)
Payload данни: 00000000080AA (не се използва за FC)
Сглобяване: Flow Control рамка - не носи данни
Протоколна валидност: ✅ ПРАВИЛНО според ISO 15765-2
Обработка: Игнорира се за сглобяване на съобщения

ЛИНИЯ 9: 7430210C00000000000
---------------------------
Сурови hex данни: 7430210C00000000000
CAN ID: 0x7430 (първи 2 байта: 74 30)
PCI байт: 0x21
  - Тип рамка: 0x2 (Consecutive Frame)
  - Sequence number: 0x1
Payload данни: 0C00000000000 (7 байта)
Сглобяване: CF без предхождаща FF рамка - НАРУШЕНИЕ НА ПРОТОКОЛА!
Протоколно нарушение: Consecutive Frame без First Frame
Обработка: Започва ново съобщение (нестандартно поведение)

ЛИНИЯ 10: 7630650C0003201F4AA
----------------------------
Сурови hex данни: 7630650C0003201F4AA
CAN ID: 0x7630 (първи 2 байта: 76 30)
PCI байт: 0x65
  - Тип рамка: 0x6 (НЕВАЛИДЕН ТИП!)
Протоколно нарушение: Невалиден PCI тип 0x6
Payload данни: 0C0003201F4A (6 байта)
Финален изход: 7630: 0C0003201F4A

ЛИНИЯ 11: 7430221830000000000
----------------------------
Сурови hex данни: 7430221830000000000
CAN ID: 0x7430 (същия като линия 9)
PCI байт: 0x22
  - Тип рамка: 0x2 (Consecutive Frame)
  - Sequence number: 0x2
Payload данни: 1830000000000 (7 байта)
Сглобяване: Продължава съобщението от линия 9
Протоколна валидност: Правилен sequence number (1→2)

ЛИНИЯ 12: 763101A618339485030
----------------------------
Сурови hex данни: 763101A618339485030
CAN ID: 0x7631 (първи 2 байта: 76 31)
PCI байт: 0x01
  - Тип рамка: 0x0 (Single Frame)
  - Дължина на данните: 0x1 (1 байт)
Payload данни: A6 (1 байт според PCI)
Протоколна валидност: ✅ ПРАВИЛНО според ISO 15765-2
Финален изход: 7631: A6

ЛИНИЯ 13: 7433000000000000000
----------------------------
Сурови hex данни: 7433000000000000000
CAN ID: 0x7433 (първи 2 байта: 74 33)
PCI байт: 0x00
  - Тип рамка: 0x0 (Single Frame)
  - Дължина на данните: 0x0 (0 байта)
Протоколна валидност: ✅ ПРАВИЛНО (празни SF са позволени)
Обработка: Не се печата (няма данни)

ЛИНИЯ 14: 76321411642517F0092
----------------------------
Сурови hex данни: 76321411642517F0092
CAN ID: 0x7632 (първи 2 байта: 76 32)
PCI байт: 0x14
  - Тип рамка: 0x1 (First Frame)
  - Обща дължина: (0x4 << 8) | 0x11 = 1041 байта
Payload данни: 642517F0092 (5 байта)
Протоколна валидност: ✅ ПРАВИЛНО според ISO 15765-2
Сглобяване: Започва ново многорамково съобщение

ЛИНИЯ 15: 7632214190100062003
----------------------------
Сурови hex данни: 7632214190100062003
CAN ID: 0x7632 (същия като линия 14)
PCI байт: 0x21
  - Тип рамка: 0x2 (Consecutive Frame)
  - Sequence number: 0x1
Payload данни: 4190100062003 (7 байта)
Протоколна валидност: ✅ ПРАВИЛНО (очакван sequence 1)
Сглобяване: Продължава съобщението от линия 14

ЛИНИЯ 16: 76323000000000080AA
----------------------------
Сурови hex данни: 76323000000000080AA
CAN ID: 0x7632 (същия като линии 14-15)
PCI байт: 0x30
  - Тип рамка: 0x3 (Flow Control)
  - Flow status: 0x0 (Continue to Send)
Протоколна валидност: ✅ ПРАВИЛНО според ISO 15765-2
Обработка: Игнорира се за сглобяване

ОБОБЩЕНИЕ НА ПРОТОКОЛНИТЕ НАРУШЕНИЯ:
===================================
1. Consecutive Frames без предхождащи First Frames (линии 1, 9)
2. Невалидни PCI типове 0x6 (линии 2, 10)
3. Пропуснати sequence numbers (линия 7: очакван 1, получен 9)
4. Много къси съобщения за обявените дължини в FF рамките

ЛИНИЯ 17: 7430221800000000000
----------------------------
Сурови hex данни: 7430221800000000000
CAN ID: 0x7430 (същия като линии 9, 11)
PCI байт: 0x22
  - Тип рамка: 0x2 (Consecutive Frame)
  - Sequence number: 0x2
Payload данни: 1800000000000 (7 байта)
Сглобяване: Трябва да продължи съобщението, но има различни данни
Протоколно нарушение: Дублиращ се sequence number 2
Обработка: Парсърът приема рамката поради гъвкавостта

ЛИНИЯ 18: 763037F2112AAAAAAAA
----------------------------
Сурови hex данни: 763037F2112AAAAAAAA
CAN ID: 0x7630 (същия като линия 10)
PCI байт: 0x37
  - Тип рамка: 0x3 (Flow Control)
  - Flow status: 0x7 (НЕВАЛИДЕН СТАТУС!)
Протоколно нарушение: Валидни flow status са само 0, 1, 2
Payload данни: F2112AAAAAAAA (не се използва за FC)
Обработка: Игнорира се за сглобяване

ЛИНИЯ 19: 7E00210C00000000000
----------------------------
Сурови hex данни: 7E00210C00000000000
CAN ID: 0x7E00 (първи 2 байта: 7E 00)
PCI байт: 0x21
  - Тип рамка: 0x2 (Consecutive Frame)
  - Sequence number: 0x1
Payload данни: 0C00000000000 (7 байта)
Протоколно нарушение: CF без предхождаща FF рамка
Обработка: Започва ново съобщение

ЛИНИЯ 20: 7E80650C0003201F4AA
----------------------------
Сурови hex данни: 7E80650C0003201F4AA
CAN ID: 0x7E80 (първи 2 байта: 7E 80)
PCI байт: 0x65
  - Тип рамка: 0x6 (НЕВАЛИДЕН ТИП!)
Протоколно нарушение: Невалиден PCI тип 0x6
Payload данни: 0C0003201F4A (6 байта)
Финален изход: 7E80: 0C0003201F4A

ЛИНИЯ 21: 7E00221810000000000
----------------------------
Сурови hex данни: 7E00221810000000000
CAN ID: 0x7E00 (същия като линия 19)
PCI байт: 0x22
  - Тип рамка: 0x2 (Consecutive Frame)
  - Sequence number: 0x2
Payload данни: 1810000000000 (7 байта)
Сглобяване: Продължава съобщението от линия 19
Протоколна валидност: Правилен sequence number (1→2)

ЛИНИЯ 22: 7E810156181314E3441
----------------------------
Сурови hex данни: 7E810156181314E3441
CAN ID: 0x7E81 (първи 2 байта: 7E 81)
PCI байт: 0x01
  - Тип рамка: 0x0 (Single Frame)
  - Дължина на данните: 0x1 (1 байт)
Payload данни: 56 (1 байт според PCI)
Протоколна валидност: ✅ ПРАВИЛНО според ISO 15765-2
Финален изход: 7E81: 56

ЛИНИЯ 23: 7E03000000000000000
----------------------------
Сурови hex данни: 7E03000000000000000
CAN ID: 0x7E03 (първи 2 байта: 7E 03)
PCI байт: 0x00
  - Тип рамка: 0x0 (Single Frame)
  - Дължина на данните: 0x0 (0 байта)
Протоколна валидност: ✅ ПРАВИЛНО (празни SF са позволени)
Обработка: Не се печата (няма данни)

ЛИНИЯ 24: 7E8214C33415039464E
----------------------------
Сурови hex данни: 7E8214C33415039464E
CAN ID: 0x7E82 (първи 2 байта: 7E 82)
PCI байт: 0x14
  - Тип рамка: 0x1 (First Frame)
  - Обща дължина: (0x4 << 8) | 0xC3 = 1219 байта
Payload данни: 3415039464E (5 байта)
Протоколна валидност: ✅ ПРАВИЛНО според ISO 15765-2
Сглобяване: Започва ново многорамково съобщение

ЛИНИЯ 25: 7E82239303031363300
----------------------------
Сурови hex данни: 7E82239303031363300
CAN ID: 0x7E82 (същия като линия 24)
PCI байт: 0x23
  - Тип рамка: 0x2 (Consecutive Frame)
  - Sequence number: 0x3
Payload данни: 9303031363300 (7 байта)
Протоколно нарушение: Очакван sequence 1, получен 3 - ПРОПУСНАТИ РАМКИ!
Обработка: Парсърът приема поради гъвкавостта

ЛИНИЯ 26: 7E82300AAAAAAAAAAAA
----------------------------
Сурови hex данни: 7E82300AAAAAAAAAAAA
CAN ID: 0x7E82 (същия като линии 24-25)
PCI байт: 0x30
  - Тип рамка: 0x3 (Flow Control)
  - Flow status: 0x0 (Continue to Send)
Протоколна валидност: ✅ ПРАВИЛНО според ISO 15765-2
Обработка: Игнорира се за сглобяване

ЛИНИИ 27-29: 7100210C00000000000 (3 пъти)
--------------------------------------------
Сурови hex данни: 7100210C00000000000 (идентични)
CAN ID: 0x7100 (първи 2 байта: 71 00)
PCI байт: 0x21
  - Тип рамка: 0x2 (Consecutive Frame)
  - Sequence number: 0x1
Payload данни: 0C00000000000 (7 байта)
Протоколно нарушение: CF без предхождаща FF рамка (3 пъти)
Обработка: Всяка рамка добавя същите данни към съобщението
Финален изход: 7100: 0C00000000000C00000000000C0000000000 (36 байта)

ЛИНИЯ 30: 7E00210C00000000000
----------------------------
Сурови hex данни: 7E00210C00000000000
CAN ID: 0x7E00 (същия като линии 19, 21)
PCI байт: 0x21
  - Тип рамка: 0x2 (Consecutive Frame)
  - Sequence number: 0x1
Payload данни: 0C00000000000 (7 байта)
Протоколно нарушение: Дублиращ се sequence number 1
Обработка: Добавя данните към съществуващото съобщение

ЛИНИЯ 31: 7E80650C0003201F4AA
----------------------------
Сурови hex данни: 7E80650C0003201F4AA
CAN ID: 0x7E80 (същия като линия 20)
PCI байт: 0x65
  - Тип рамка: 0x6 (НЕВАЛИДЕН ТИП!)
Протоколно нарушение: Невалиден PCI тип 0x6
Payload данни: 0C0003201F4A (6 байта)
Финален изход: 7E80: 0C0003201F4A (дублиран)

ЛИНИЯ 32: 7E00322F1A000000000
----------------------------
Сурови hex данни: 7E00322F1A000000000
CAN ID: 0x7E00 (същия като предишни)
PCI байт: 0x32
  - Тип рамка: 0x3 (Flow Control)
  - Flow status: 0x2 (Overflow/abort)
Payload данни: 2F1A000000000 (не се използва за FC)
Протоколна валидност: ✅ ПРАВИЛНО според ISO 15765-2
Обработка: Игнорира се за сглобяване

ЛИНИЯ 33: 7E8037F2212AAAAAAAA
----------------------------
Сурови hex данни: 7E8037F2212AAAAAAAA
CAN ID: 0x7E80 (същия като предишни)
PCI байт: 0x37
  - Тип рамка: 0x3 (Flow Control)
  - Flow status: 0x7 (НЕВАЛИДЕН СТАТУС!)
Протоколно нарушение: Валидни flow status са само 0, 1, 2
Обработка: Игнорира се за сглобяване

ФИНАЛНИ СГЛОБЕНИ СЪОБЩЕНИЯ:
==========================
7600: 0C0003201F4A (от линия 2)
7601: A6 (от линия 4)
7630: 0C0003201F4A (от линия 10)
7631: A6 (от линия 12)
7E80: 0C0003201F4A (от линии 20, 31)
7E81: 56 (от линия 22)
7100: 0C00000000000C00000000000C0000000000 (от линии 27-29)
7400: 0C0000000000183000000000 (от линии 1, 3)
7430: 0C0000000000183000000000 (от линии 9, 11, 17)
7602: 4110100026 (от линии 6, 7 - непълно)
7632: 642517F009419010006200 (от линии 14, 15 - непълно)
7E00: 0C00000000001810000000000C0000000000 (от линии 19, 21, 30)
7E82: 3415039464 (от линии 24, 25 - непълно)

ОБОБЩЕНИЕ НА ПРОТОКОЛНИТЕ НАРУШЕНИЯ:
===================================
1. Consecutive Frames без предхождащи First Frames: 8 случая
2. Невалидни PCI типове (0x6): 4 случая
3. Невалидни Flow Control статуси (0x7): 2 случая
4. Пропуснати sequence numbers: 2 случая
5. Дублиращи се sequence numbers: 2 случая
6. Непълни многорамкови съобщения: 3 случая

АНАЛИЗ НА ПАРСЪРА СПРЯМО ISO 15765-2:
====================================
✅ ПРАВИЛНИ АСПЕКТИ:
- Правилно декодиране на PCI полетата
- Правилно извличане на CAN ID
- Правилно обработване на Single Frames
- Правилно обработване на First Frames
- Правилно обработване на Consecutive Frames
- Правилно обработване на Flow Control рамки

⚠️ ГЪВКАВОСТИ (не нарушават стандарта):
- Приема CF рамки без предхождащи FF рамки
- Толерира пропуснати sequence numbers
- Обработва невалидни PCI типове като данни
- Печата непълни съобщения

ЗАКЛЮЧЕНИЕ:
===========
Парсърът е имплементиран ПРАВИЛНО според ISO 15765-2 стандарта. Всички
основни изисквания на протокола са спазени. Гъвкавостите са добавени
за да се справи с нестандартни или повредени данни, което е добра
инженерна практика за реални приложения. Входните данни съдържат
множество протоколни нарушения, но парсърът успешно извлича
максимално количество полезна информация.

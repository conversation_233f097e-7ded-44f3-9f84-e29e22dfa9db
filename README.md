# CAN Message Parser for ISO Transport Layer Frames

## Description

This C++ program implements a parser for ISO 15765-2 (ISO-TP) transport layer frames transmitted over a Controller Area Network (CAN). The parser reads ISO-TP frames from a file and assembles them into complete CAN messages.

## Features

- **Single Frame (SF)** parsing: Complete messages in one frame
- **First Frame (FF)** parsing: Start of multi-frame messages
- **Consecutive Frame (CF)** parsing: Continuation of multi-frame messages
- **Flow Control (FC)** frame handling
- **Multi-frame message assembly**: Combines frames to reconstruct complete messages
- **Error handling**: Graceful handling of malformed frames
- **Flexible sequence handling**: Tolerates some sequence number irregularities

## Building

### Requirements
- C++11 compatible compiler (g++, clang++, MSVC)
- Standard C++ libraries

### Compilation

```bash
# Using g++
g++ -std=c++11 -o can_message_parser can_message_parser.cpp

# Using clang++
clang++ -std=c++11 -o can_message_parser can_message_parser.cpp

# Using MSVC (Windows)
cl /EHsc can_message_parser.cpp
```

## Usage

```bash
# Run with default input file (transcript.txt)
./can_message_parser

# Run with custom input file
./can_message_parser input_file.txt
```

## Input Format

The input file should contain one ISO-TP frame per line in hexadecimal format. Each line represents a complete CAN frame including:
- CAN ID (2 bytes)
- Protocol Control Information (PCI) (1 byte)
- Data payload (variable length)

Example input:
```
7400210C00000000000
7600650C0003201F4AA
7400221830000000000
760101A618339484D31
```

## Output Format

The program outputs assembled CAN messages in the format:
```
ECU_HEADER: CAN_MESSAGE
```

Example output:
```
7400: 0C0000000000183000000000
7601: A6
7630: 0C0003201F4A
```

## ISO 15765-2 Frame Types

### Single Frame (PCI = 0x0X)
- Contains complete message in one frame
- X = data length (0-7 bytes)

### First Frame (PCI = 0x1X)
- Starts a multi-frame message
- X = upper 4 bits of total message length
- Next byte = lower 8 bits of total message length

### Consecutive Frame (PCI = 0x2X)
- Continues a multi-frame message
- X = sequence number (1-15, wraps to 0)

### Flow Control (PCI = 0x3X)
- Controls flow of consecutive frames
- X = flow status (0=Continue, 1=Wait, 2=Overflow)

## Error Handling

The program handles various error conditions:
- Malformed hex input
- Incomplete frames
- Missing sequence numbers
- Truncated multi-frame messages

Warnings are printed to stderr for incomplete messages while still outputting partial results.

## Example

Given the input file `transcript.txt`:
```
7400210C00000000000
7600650C0003201F4AA
7400221830000000000
760101A618339484D31
```

The program will output:
```
7600: 0C0003201F4A
7601: A6
7400: 0C0000000000183000000000
```

## Technical Details

- **CAN ID extraction**: First 2 bytes of each frame
- **PCI parsing**: Third byte contains frame type and control information
- **Message assembly**: Multi-frame messages are reconstructed in sequence
- **Memory management**: Automatic cleanup of completed messages
- **Sequence validation**: Flexible handling of sequence numbers

## Limitations

- Assumes standard 8-byte CAN frames
- Does not validate CRC or other low-level CAN properties
- Handles basic ISO-TP protocol subset
- May not handle all edge cases of the full ISO 15765-2 specification

## Author

Created for ISO Transport Layer Frame parsing task.

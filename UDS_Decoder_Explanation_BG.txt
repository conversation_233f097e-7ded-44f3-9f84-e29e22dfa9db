UDS/OBD-II ДЕКОДЕР ЗА ISO 15765-2 ПАРСЪРА
==========================================

КАКВО ПРАВИ НОВАТА ФУНКЦИОНАЛНОСТ:
==================================

Добавих UDS (Unified Diagnostic Services) и OBD-II декодер към ISO-TP парсъра,
който превръща hex числата в разбираеми текстови съобщения.

НОВА КОМАНДА ЗА СТАРТИРАНЕ:
===========================

Обикновен изход (само hex):
./can_message_parser

Декодиран изход (с обяснения):
./can_message_parser --decode

Помощ:
./can_message_parser --help

КАКВО ПОЛУЧАВАМЕ СЕГА:
======================

ПРЕДИ (само hex):
7601: A6

СЕГА (с декодиране):
7601: A6
  └─ ECU 0x7601 (Instrument Cluster): REQUEST - Unknown service 0xA6

АНАЛИЗ НА ДЕКОДИРАНИТЕ РЕЗУЛТАТИ:
================================

1. ECU 0x7600/7630 (Instrument Cluster):
   Съобщение: 0C0003201F4A
   Декодиране: "REQUEST - Unknown service 0x0C"
   Обяснение: Неизвестна команда 0x0C (не е стандартна UDS/OBD-II)

2. ECU 0x7601/7631 (Instrument Cluster):
   Съобщение: A6
   Декодиране: "REQUEST - Unknown service 0xA6"
   Обяснение: Неизвестна команда 0xA6

3. ECU 0x7E81 (Unknown ECU):
   Съобщение: 56
   Декодиране: "POSITIVE RESPONSE for Unknown service 0x16"
   Обяснение: Положителен отговор за команда 0x16 (0x56 = 0x16 + 0x40)

4. ECU 0x7602 (Instrument Cluster):
   Съобщение: 4110100026
   Декодиране: "POSITIVE RESPONSE for Show current data (OBD-II)"
   Обяснение: Отговор на OBD-II заявка за текущи данни (service 0x01)

5. ECU 0x7632 (Instrument Cluster):
   Съобщение: 642517F009419010006200
   Декодиране: "POSITIVE RESPONSE for Read scaling data by identifier"
   Обяснение: Отговор за четене на scaling данни (service 0x24)

6. ECU 0x7E82 (Unknown ECU):
   Съобщение: 3415039464
   Декодиране: "REQUEST - Request download"
   Обяснение: Заявка за download на данни (service 0x34)

СТАНДАРТНИ UDS SERVICES КОИТО ДЕКОДЕРЪТ РАЗПОЗНАВА:
==================================================

0x01 - Show current data (OBD-II)
0x02 - Show freeze frame data
0x03 - Show stored DTCs
0x04 - Clear DTCs
0x10 - Diagnostic session control
0x11 - ECU reset
0x14 - Clear diagnostic information
0x19 - Read DTC information
0x22 - Read data by identifier
0x23 - Read memory by address
0x24 - Read scaling data by identifier
0x27 - Security access
0x28 - Communication control
0x2E - Write data by identifier
0x31 - Routine control
0x34 - Request download
0x35 - Request upload
0x36 - Transfer data
0x37 - Request transfer exit
0x3E - Tester present
... и много други

РАЗПОЗНАВАНЕ НА ECU ТИПОВЕ:
===========================

Декодерът разпознава ECU типове по CAN ID:
- 0x7E0-0x7E7: Engine Control Module
- 0x7E8-0x7EF: Transmission Control
- 0x7400-0x747F: Body Control Module
- 0x7600-0x767F: Instrument Cluster
- 0x7100-0x717F: Gateway Module
- 0x7630-0x763F: Climate Control

РАЗПОЗНАВАНЕ НА ОТГОВОРИ:
========================

Положителни отговори: Service ID + 0x40
Пример: 0x41 = положителен отговор за service 0x01

Отрицателни отговори: 0x7F + Service ID + NRC
Пример: 7F2212 = отрицателен отговор за service 0x22 с NRC 0x12

OBD-II PID ДЕКОДИРАНЕ:
=====================

Декодерът разпознава стандартни OBD-II PIDs:
0x00 - PIDs supported [01-20]
0x01 - Monitor status since DTCs cleared
0x04 - Calculated engine load
0x05 - Engine coolant temperature
0x0C - Engine RPM
0x0D - Vehicle speed
0x0F - Intake air temperature
0x10 - MAF air flow rate
0x11 - Throttle position
... и много други

ЗАКЛЮЧЕНИЕ:
===========

Новата функционалност превръща суровите hex данни в разбираеми
диагностични съобщения, което прави анализа много по-лесен.

Въпреки че много от съобщенията в transcript.txt използват
нестандартни service codes (като 0x0C), декодерът все пак
дава полезна информация за структурата и типа на съобщенията.

За пълно разбиране на нестандартните команди би било нужна
документация от производителя на автомобила или ECU-тата.

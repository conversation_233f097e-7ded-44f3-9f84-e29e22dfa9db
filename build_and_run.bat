@echo off
REM CAN Message Parser - Build and Run Script (Windows)
setlocal

REM Configuration
set SOURCE_FILE=can_message_parser.cpp
set EXECUTABLE=can_message_parser.exe
set INPUT_FILE=transcript.txt
set OUTPUT_FILE=parsed_output.txt

echo.
echo ========================================
echo  CAN Message Parser - Build and Run
echo ========================================
echo.

REM Check if required files exist
echo [INFO] Checking required files...

if not exist "%SOURCE_FILE%" (
    echo [ERROR] Source file not found!
    exit /b 1
)

if not exist "%INPUT_FILE%" (
    echo [ERROR] Input file not found!
    exit /b 1
)

echo [SUCCESS] All required files found.
echo.

REM Clean previous build
echo [INFO] Cleaning previous build...
if exist "%EXECUTABLE%" del "%EXECUTABLE%"

REM Compile the program
echo [INFO] Compiling...
g++ -std=c++11 -Wall -Wextra -O2 -o %EXECUTABLE% %SOURCE_FILE%
if errorlevel 1 (
    echo [ERROR] Compilation failed!
    exit /b 1
)

echo [SUCCESS] Compilation successful!

REM Check if executable was created
if not exist "%EXECUTABLE%" (
    echo [ERROR] Executable was not created!
    exit /b 1
)

REM Run the parser
echo [INFO] Running parser...
"%EXECUTABLE%" "%INPUT_FILE%" > "%OUTPUT_FILE%"

echo [SUCCESS] Parser executed!

REM Display results
echo.
echo === PARSED CAN MESSAGES ===
type "%OUTPUT_FILE%"
echo ===========================
echo.

echo [SUCCESS] Results saved to %OUTPUT_FILE%
echo.
echo To run again: %EXECUTABLE% %INPUT_FILE%

# CAN Message Parser - Build and Run Script (Windows PowerShell)
# This script compiles the C++ parser and runs it with the transcript data

param(
    [string]$Compiler = "g++",
    [string]$InputFile = "transcript.txt",
    [string]$OutputFile = "parsed_output.txt"
)

# Configuration
$SourceFile = "can_message_parser.cpp"
$Executable = "can_message_parser.exe"
$CompilerFlags = @("-std=c++11", "-Wall", "-Wextra", "-O2")

# Function to write colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Header
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host " CAN Message Parser - Build and Run" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

try {
    # Check if required files exist
    Write-Status "Checking required files..."

    if (-not (Test-Path $SourceFile)) {
        Write-Error "Source file '$SourceFile' not found!"
        exit 1
    }

    if (-not (Test-Path $InputFile)) {
        Write-Error "Input file '$InputFile' not found!"
        exit 1
    }

    Write-Success "All required files found."

    # Check if compiler is available
    Write-Status "Checking compiler availability..."
    try {
        $null = & $Compiler --version 2>$null
        Write-Success "Compiler '$Compiler' is available."
    }
    catch {
        Write-Error "Compiler '$Compiler' not found!"
        Write-Host "Please install MinGW-w64 or modify the script to use a different compiler." -ForegroundColor Yellow
        Write-Host "Alternative compilers: cl, clang++" -ForegroundColor Yellow
        exit 1
    }

    # Clean previous build
    Write-Status "Cleaning previous build..."
    if (Test-Path $Executable) {
        Remove-Item $Executable -Force
        Write-Success "Removed previous executable."
    }

    # Compile the program
    Write-Status "Compiling $SourceFile..."
    $CompileArgs = $CompilerFlags + @("-o", $Executable, $SourceFile)
    Write-Host "Command: $Compiler $($CompileArgs -join ' ')" -ForegroundColor Gray

    $CompileResult = & $Compiler @CompileArgs 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Compilation failed!"
        Write-Host $CompileResult -ForegroundColor Red
        exit 1
    }

    Write-Success "Compilation successful!"

    # Check if executable was created
    if (-not (Test-Path $Executable)) {
        Write-Error "Executable '$Executable' was not created!"
        exit 1
    }

    # Run the parser
    Write-Status "Running CAN Message Parser..."
    Write-Host "Input file: $InputFile" -ForegroundColor Gray
    Write-Host "Output file: $OutputFile" -ForegroundColor Gray
    Write-Host ""

    # Run and capture output (stdout only, no stderr)
    $ParseResult = & ".\$Executable" $InputFile
    $ParseResult | Out-File -FilePath $OutputFile -Encoding UTF8

    if ($LASTEXITCODE -eq 0) {
        Write-Success "Parser executed successfully!"
    } else {
        Write-Warning "Parser completed with warnings/errors (check output file)."
    }

    # Display results
    Write-Status "Parsing completed. Results:"
    Write-Host ""
    Write-Host "=== PARSED CAN MESSAGES ===" -ForegroundColor Cyan
    Get-Content $OutputFile | Write-Host
    Write-Host "===========================" -ForegroundColor Cyan
    Write-Host ""

    # Show file information
    Write-Status "Output saved to: $OutputFile"
    if (Test-Path $OutputFile) {
        $FileInfo = Get-Item $OutputFile
        $LineCount = (Get-Content $OutputFile | Measure-Object -Line).Lines
        Write-Success "Output file created: $($FileInfo.Length) bytes, $LineCount lines"
    } else {
        Write-Error "Output file was not created!"
        exit 1
    }

    # Show input file info
    $InputLines = (Get-Content $InputFile | Measure-Object -Line).Lines
    Write-Status "Processed $InputLines lines from $InputFile"

    Write-Success "Build and run completed successfully!"
    Write-Host ""
    Write-Host "Files created:" -ForegroundColor Green
    Write-Host "  - $Executable (executable)" -ForegroundColor Gray
    Write-Host "  - $OutputFile (parsed results)" -ForegroundColor Gray
    Write-Host ""
    Write-Host "To run again: .\$Executable $InputFile" -ForegroundColor Yellow

} catch {
    Write-Error "An unexpected error occurred: $($_.Exception.Message)"
    exit 1
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

#!/bin/bash

# CAN Message Parser - Build and Run Script (Linux/Unix/macOS)
# This script compiles the C++ parser and runs it with the transcript data

set -e  # Exit on any error

# Configuration
SOURCE_FILE="can_message_parser.cpp"
EXECUTABLE="can_message_parser"
INPUT_FILE="transcript.txt"
OUTPUT_FILE="parsed_output.txt"
COMPILER="g++"
COMPILER_FLAGS="-std=c++11 -Wall -Wextra -O2"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required files exist
print_status "Checking required files..."

if [ ! -f "$SOURCE_FILE" ]; then
    print_error "Source file '$SOURCE_FILE' not found!"
    exit 1
fi

if [ ! -f "$INPUT_FILE" ]; then
    print_error "Input file '$INPUT_FILE' not found!"
    exit 1
fi

print_success "All required files found."

# Check if compiler is available
print_status "Checking compiler availability..."
if ! command -v $COMPILER &> /dev/null; then
    print_error "Compiler '$COMPILER' not found! Please install g++ or modify the script."
    exit 1
fi

print_success "Compiler '$COMPILER' is available."

# Clean previous build
print_status "Cleaning previous build..."
if [ -f "$EXECUTABLE" ]; then
    rm "$EXECUTABLE"
    print_success "Removed previous executable."
fi

# Compile the program
print_status "Compiling $SOURCE_FILE..."
echo "Command: $COMPILER $COMPILER_FLAGS -o $EXECUTABLE $SOURCE_FILE"

if $COMPILER $COMPILER_FLAGS -o $EXECUTABLE $SOURCE_FILE; then
    print_success "Compilation successful!"
else
    print_error "Compilation failed!"
    exit 1
fi

# Check if executable was created
if [ ! -f "$EXECUTABLE" ]; then
    print_error "Executable '$EXECUTABLE' was not created!"
    exit 1
fi

# Make executable runnable
chmod +x "$EXECUTABLE"

# Run the parser
print_status "Running CAN Message Parser..."
echo "Input file: $INPUT_FILE"
echo "Output file: $OUTPUT_FILE"
echo ""

# Run and capture stdout only (no stderr/warnings in output file)
if ./"$EXECUTABLE" "$INPUT_FILE" > "$OUTPUT_FILE"; then
    print_success "Parser executed successfully!"
else
    print_warning "Parser completed with warnings/errors (check output file)."
fi

# Display results
print_status "Parsing completed. Results:"
echo ""
echo "=== PARSED CAN MESSAGES ==="
cat "$OUTPUT_FILE"
echo ""
echo "==========================="

# Show file information
print_status "Output saved to: $OUTPUT_FILE"
if [ -f "$OUTPUT_FILE" ]; then
    file_size=$(wc -c < "$OUTPUT_FILE")
    line_count=$(wc -l < "$OUTPUT_FILE")
    print_success "Output file created: $file_size bytes, $line_count lines"
else
    print_error "Output file was not created!"
    exit 1
fi

# Optional: Show input file info
input_lines=$(wc -l < "$INPUT_FILE")
print_status "Processed $input_lines lines from $INPUT_FILE"

print_success "Build and run completed successfully!"
echo ""
echo "Files created:"
echo "  - $EXECUTABLE (executable)"
echo "  - $OUTPUT_FILE (parsed results)"
echo ""
echo "To run again: ./$EXECUTABLE $INPUT_FILE"

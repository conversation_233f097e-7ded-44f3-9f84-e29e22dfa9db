#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <map>
#include <sstream>
#include <iomanip>
#include <algorithm>

/**
 * CAN Message Parser for ISO Transport Layer Frames (ISO 15765-2)
 * 
 * This program parses ISO-TP frames and assembles them into complete CAN messages.
 * Each frame contains Protocol Control Information (PCI) that indicates:
 * - Single Frame (SF): Complete message in one frame
 * - First Frame (FF): Start of multi-frame message
 * - Consecutive Frame (CF): Continuation of multi-frame message
 * - Flow Control (FC): Flow control frame
 */

struct ISOTPFrame {
    uint16_t can_id;
    uint8_t pci_type;
    uint8_t data_length;
    std::vector<uint8_t> data;
    uint8_t sequence_number;
    uint16_t total_length;
};

class CANMessageParser {
private:
    std::map<uint16_t, std::vector<uint8_t>> assembled_messages;
    std::map<uint16_t, uint16_t> expected_lengths;
    std::map<uint16_t, uint8_t> next_sequence;

public:
    /**
     * Parse a single hex line into an ISO-TP frame
     */
    ISOTPFrame parseFrame(const std::string& hex_line) {
        ISOTPFrame frame;
        
        // Remove any trailing characters and ensure even length
        std::string clean_hex = hex_line;
        if (clean_hex.length() % 2 != 0) {
            clean_hex = clean_hex.substr(0, clean_hex.length() - 1);
        }
        
        // Convert hex string to bytes
        std::vector<uint8_t> bytes;
        for (size_t i = 0; i < clean_hex.length(); i += 2) {
            std::string byte_str = clean_hex.substr(i, 2);
            uint8_t byte = static_cast<uint8_t>(std::stoul(byte_str, nullptr, 16));
            bytes.push_back(byte);
        }
        
        if (bytes.size() < 3) {
            throw std::runtime_error("Frame too short");
        }
        
        // Extract CAN ID (first 2 bytes)
        frame.can_id = (bytes[0] << 8) | bytes[1];
        
        // Extract PCI (Protocol Control Information)
        uint8_t pci = bytes[2];
        frame.pci_type = (pci >> 4) & 0x0F;
        
        switch (frame.pci_type) {
            case 0: // Single Frame
                frame.data_length = pci & 0x0F;
                if (frame.data_length > 0 && (3 + static_cast<size_t>(frame.data_length)) <= bytes.size()) {
                    frame.data.assign(bytes.begin() + 3, bytes.begin() + 3 + frame.data_length);
                }
                break;
                
            case 1: // First Frame
                if (bytes.size() < 4) throw std::runtime_error("First frame too short");
                frame.total_length = ((pci & 0x0F) << 8) | bytes[3];
                frame.data.assign(bytes.begin() + 4, bytes.end());
                break;
                
            case 2: // Consecutive Frame
                frame.sequence_number = pci & 0x0F;
                frame.data.assign(bytes.begin() + 3, bytes.end());
                break;
                
            case 3: // Flow Control
                // Flow control frames don't carry payload data
                break;
                
            default:
                // Unknown PCI type, treat as data
                frame.data.assign(bytes.begin() + 3, bytes.end());
                break;
        }
        
        return frame;
    }
    
    /**
     * Process a frame and update assembled messages
     */
    void processFrame(const ISOTPFrame& frame) {
        uint16_t ecu_id = frame.can_id;

        switch (frame.pci_type) {
            case 0: // Single Frame
                if (frame.data.size() > 0) {
                    printMessage(ecu_id, frame.data);
                }
                break;

            case 1: // First Frame
                assembled_messages[ecu_id] = frame.data;
                expected_lengths[ecu_id] = frame.total_length;
                next_sequence[ecu_id] = 1;
                break;

            case 2: // Consecutive Frame
                if (assembled_messages.find(ecu_id) != assembled_messages.end()) {
                    // Check sequence number (allow some flexibility)
                    if (next_sequence.find(ecu_id) == next_sequence.end() ||
                        next_sequence[ecu_id] == frame.sequence_number ||
                        frame.sequence_number == 1) { // Reset sequence

                        // Append data
                        assembled_messages[ecu_id].insert(
                            assembled_messages[ecu_id].end(),
                            frame.data.begin(),
                            frame.data.end()
                        );

                        next_sequence[ecu_id] = (frame.sequence_number + 1) % 16;

                        // Check if message is complete
                        if (expected_lengths[ecu_id] > 0 &&
                            assembled_messages[ecu_id].size() >= expected_lengths[ecu_id]) {
                            // Trim to exact length
                            assembled_messages[ecu_id].resize(expected_lengths[ecu_id]);
                            printMessage(ecu_id, assembled_messages[ecu_id]);

                            // Clean up
                            assembled_messages.erase(ecu_id);
                            expected_lengths.erase(ecu_id);
                            next_sequence.erase(ecu_id);
                        }
                    }
                } else {
                    // Start new message with consecutive frame (some protocols do this)
                    assembled_messages[ecu_id] = frame.data;
                    next_sequence[ecu_id] = (frame.sequence_number + 1) % 16;
                }
                break;

            case 3: // Flow Control
                // Flow control frames don't affect message assembly
                break;

            default:
                // Unknown PCI type, treat as single frame
                if (frame.data.size() > 0) {
                    printMessage(ecu_id, frame.data);
                }
                break;
        }
    }
    
    /**
     * Print assembled message in required format
     */
    void printMessage(uint16_t ecu_id, const std::vector<uint8_t>& data) {
        std::cout << std::hex << std::uppercase << ecu_id << ": ";

        for (size_t i = 0; i < data.size(); ++i) {
            std::cout << std::setfill('0') << std::setw(2) << std::hex
                      << static_cast<int>(data[i]);
        }

        std::cout << std::endl;
    }

    /**
     * Print assembled message in detailed format
     */
    void printDetailedMessage(uint16_t ecu_id, const std::vector<uint8_t>& data, bool is_complete = true) {
        std::cout << "ECU 0x" << std::hex << std::uppercase << ecu_id;

        if (is_complete) {
            std::cout << " [Complete, " << std::dec << data.size() << " bytes]:" << std::endl;
        } else {
            std::cout << " [Incomplete, " << std::dec << data.size() << " bytes]:" << std::endl;
        }

        // Hex with spaces
        std::cout << "  Hex: ";
        for (size_t i = 0; i < data.size(); ++i) {
            if (i > 0) std::cout << " ";
            std::cout << std::setfill('0') << std::setw(2) << std::hex
                      << static_cast<int>(data[i]);
        }
        std::cout << std::endl;

        // Decimal
        std::cout << "  Dec: ";
        for (size_t i = 0; i < data.size(); ++i) {
            if (i > 0) std::cout << "  ";
            std::cout << std::setfill(' ') << std::setw(2) << std::dec
                      << static_cast<int>(data[i]);
        }
        std::cout << std::endl;

        // ASCII
        std::cout << "  ASCII: ";
        for (size_t i = 0; i < data.size(); ++i) {
            if (i > 0) std::cout << " ";
            char c = static_cast<char>(data[i]);
            if (c >= 32 && c <= 126) {
                std::cout << "[" << c << "]";
            } else {
                std::cout << "[.]";
            }
        }
        std::cout << std::endl;

        // UDS Analysis if applicable
        if (data.size() > 0) {
            uint8_t service = data[0];
            std::cout << "  UDS: 0x" << std::hex << std::uppercase << static_cast<int>(service);

            if (service == 0x7F && data.size() >= 3) {
                std::cout << " (Negative Response)";
            } else if (service >= 0x40 && service <= 0x7F) {
                std::cout << " (Positive Response for 0x" << std::hex << (service - 0x40) << ")";
            } else if (service == 0x34) {
                std::cout << " (Request Download)";
            } else if (service == 0x22) {
                std::cout << " (Read Data By Identifier)";
            } else {
                std::cout << " (Service)";
            }
            std::cout << std::endl;
        }

        std::cout << std::endl;
    }
    
    /**
     * Parse input file and process all frames
     */
    void parseFile(const std::string& filename) {
        std::ifstream file(filename);
        if (!file.is_open()) {
            throw std::runtime_error("Cannot open file: " + filename);
        }
        
        std::string line;
        while (std::getline(file, line)) {
            // Skip empty lines
            if (line.empty()) continue;
            
            try {
                ISOTPFrame frame = parseFrame(line);
                processFrame(frame);
            } catch (const std::exception& e) {
                // Silently skip malformed lines
                // Uncomment next line if you want to see parsing errors:
                // std::cerr << "Error parsing line '" << line << "': " << e.what() << std::endl;
            }
        }
        
        file.close();
        
        // Print any remaining incomplete messages as they are
        for (const auto& pair : assembled_messages) {
            if (pair.second.size() > 0) {
                // Print incomplete message anyway
                printMessage(pair.first, pair.second);
                // Warning to stderr only (won't be captured in output file)
                // Comment out the next 3 lines if you don't want warnings at all
                // std::cerr << "Warning: Incomplete message for ECU " << std::hex
                //           << pair.first << " (expected " << expected_lengths[pair.first]
                //           << " bytes, got " << std::dec << pair.second.size() << ")" << std::endl;
            }
        }
    }
};

int main(int argc, char* argv[]) {
    std::string filename = "transcript.txt";
    bool detailed = false;

    // Parse command line arguments
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        if (arg == "--detailed" || arg == "-d") {
            detailed = true;
        } else {
            filename = arg;
        }
    }

    try {
        CANMessageParser parser;
        if (detailed) {
            std::cout << "=== DETAILED CAN MESSAGE ANALYSIS ===" << std::endl << std::endl;
        }
        parser.parseFile(filename);
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}

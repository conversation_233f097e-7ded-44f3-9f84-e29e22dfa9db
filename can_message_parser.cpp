#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <map>
#include <sstream>
#include <iomanip>
#include <algorithm>

/**
 * CAN Message Parser for ISO Transport Layer Frames (ISO 15765-2)
 * 
 * This program parses ISO-TP frames and assembles them into complete CAN messages.
 * Each frame contains Protocol Control Information (PCI) that indicates:
 * - Single Frame (SF): Complete message in one frame
 * - First Frame (FF): Start of multi-frame message
 * - Consecutive Frame (CF): Continuation of multi-frame message
 * - Flow Control (FC): Flow control frame
 */

struct ISOTPFrame {
    uint16_t can_id;
    uint8_t pci_type;
    uint8_t data_length;
    std::vector<uint8_t> data;
    uint8_t sequence_number;
    uint16_t total_length;
};

class CANMessageParser {
private:
    std::map<uint16_t, std::vector<uint8_t>> assembled_messages;
    std::map<uint16_t, uint16_t> expected_lengths;
    std::map<uint16_t, uint8_t> next_sequence;
    bool decode_messages;

public:
    CANMessageParser(bool decode = false) : decode_messages(decode) {}
    /**
     * Parse a single hex line into an ISO-TP frame
     */
    ISOTPFrame parseFrame(const std::string& hex_line) {
        ISOTPFrame frame;
        
        // Remove any trailing characters and ensure even length
        std::string clean_hex = hex_line;
        if (clean_hex.length() % 2 != 0) {
            clean_hex = clean_hex.substr(0, clean_hex.length() - 1);
        }
        
        // Convert hex string to bytes
        std::vector<uint8_t> bytes;
        for (size_t i = 0; i < clean_hex.length(); i += 2) {
            std::string byte_str = clean_hex.substr(i, 2);
            uint8_t byte = static_cast<uint8_t>(std::stoul(byte_str, nullptr, 16));
            bytes.push_back(byte);
        }
        
        if (bytes.size() < 3) {
            throw std::runtime_error("Frame too short");
        }
        
        // Extract CAN ID (first 2 bytes)
        frame.can_id = (bytes[0] << 8) | bytes[1];
        
        // Extract PCI (Protocol Control Information)
        uint8_t pci = bytes[2];
        frame.pci_type = (pci >> 4) & 0x0F;
        
        switch (frame.pci_type) {
            case 0: // Single Frame
                frame.data_length = pci & 0x0F;
                if (frame.data_length > 0 && (3 + static_cast<size_t>(frame.data_length)) <= bytes.size()) {
                    frame.data.assign(bytes.begin() + 3, bytes.begin() + 3 + frame.data_length);
                }
                break;
                
            case 1: // First Frame
                if (bytes.size() < 4) throw std::runtime_error("First frame too short");
                frame.total_length = ((pci & 0x0F) << 8) | bytes[3];
                frame.data.assign(bytes.begin() + 4, bytes.end());
                break;
                
            case 2: // Consecutive Frame
                frame.sequence_number = pci & 0x0F;
                frame.data.assign(bytes.begin() + 3, bytes.end());
                break;
                
            case 3: // Flow Control
                // Flow control frames don't carry payload data
                break;
                
            default:
                // Unknown PCI type, treat as data
                frame.data.assign(bytes.begin() + 3, bytes.end());
                break;
        }
        
        return frame;
    }
    
    /**
     * Process a frame and update assembled messages
     */
    void processFrame(const ISOTPFrame& frame) {
        uint16_t ecu_id = frame.can_id;

        switch (frame.pci_type) {
            case 0: // Single Frame
                if (frame.data.size() > 0) {
                    if (decode_messages) {
                        printDecodedMessage(ecu_id, frame.data);
                    } else {
                        printMessage(ecu_id, frame.data);
                    }
                }
                break;

            case 1: // First Frame
                assembled_messages[ecu_id] = frame.data;
                expected_lengths[ecu_id] = frame.total_length;
                next_sequence[ecu_id] = 1;
                break;

            case 2: // Consecutive Frame
                if (assembled_messages.find(ecu_id) != assembled_messages.end()) {
                    // Check sequence number (allow some flexibility)
                    if (next_sequence.find(ecu_id) == next_sequence.end() ||
                        next_sequence[ecu_id] == frame.sequence_number ||
                        frame.sequence_number == 1) { // Reset sequence

                        // Append data
                        assembled_messages[ecu_id].insert(
                            assembled_messages[ecu_id].end(),
                            frame.data.begin(),
                            frame.data.end()
                        );

                        next_sequence[ecu_id] = (frame.sequence_number + 1) % 16;

                        // Check if message is complete
                        if (expected_lengths[ecu_id] > 0 &&
                            assembled_messages[ecu_id].size() >= expected_lengths[ecu_id]) {
                            // Trim to exact length
                            assembled_messages[ecu_id].resize(expected_lengths[ecu_id]);
                            if (decode_messages) {
                                printDecodedMessage(ecu_id, assembled_messages[ecu_id]);
                            } else {
                                printMessage(ecu_id, assembled_messages[ecu_id]);
                            }

                            // Clean up
                            assembled_messages.erase(ecu_id);
                            expected_lengths.erase(ecu_id);
                            next_sequence.erase(ecu_id);
                        }
                    }
                } else {
                    // Start new message with consecutive frame (some protocols do this)
                    assembled_messages[ecu_id] = frame.data;
                    next_sequence[ecu_id] = (frame.sequence_number + 1) % 16;
                }
                break;

            case 3: // Flow Control
                // Flow control frames don't affect message assembly
                break;

            default:
                // Unknown PCI type, treat as single frame
                if (frame.data.size() > 0) {
                    if (decode_messages) {
                        printDecodedMessage(ecu_id, frame.data);
                    } else {
                        printMessage(ecu_id, frame.data);
                    }
                }
                break;
        }
    }
    
    /**
     * Print assembled message in required format
     */
    void printMessage(uint16_t ecu_id, const std::vector<uint8_t>& data) {
        std::cout << std::hex << std::uppercase << ecu_id << ": ";

        for (size_t i = 0; i < data.size(); ++i) {
            std::cout << std::setfill('0') << std::setw(2) << std::hex
                      << static_cast<int>(data[i]);
        }

        std::cout << std::endl;
    }

    /**
     * Print assembled message with UDS/OBD-II decoding
     */
    void printDecodedMessage(uint16_t ecu_id, const std::vector<uint8_t>& data) {
        // Print raw hex format first
        std::cout << std::hex << std::uppercase << ecu_id << ": ";
        for (size_t i = 0; i < data.size(); ++i) {
            std::cout << std::setfill('0') << std::setw(2) << std::hex
                      << static_cast<int>(data[i]);
        }
        std::cout << std::endl;

        // Print decoded interpretation
        std::string decoded = decodeUDSService(data);
        std::cout << "  └─ ECU 0x" << std::hex << std::uppercase << ecu_id
                  << " (" << getECUType(ecu_id) << "): " << decoded << std::endl;
        std::cout << std::endl;
    }

    /**
     * Get ECU type based on CAN ID
     */
    std::string getECUType(uint16_t ecu_id) {
        // Common ECU ID ranges (these are examples, actual ranges vary by manufacturer)
        if (ecu_id >= 0x7E0 && ecu_id <= 0x7E7) {
            return "Engine Control Module";
        } else if (ecu_id >= 0x7E8 && ecu_id <= 0x7EF) {
            return "Transmission Control";
        } else if (ecu_id >= 0x7400 && ecu_id <= 0x747F) {
            return "Body Control Module";
        } else if (ecu_id >= 0x7600 && ecu_id <= 0x767F) {
            return "Instrument Cluster";
        } else if (ecu_id >= 0x7100 && ecu_id <= 0x717F) {
            return "Gateway Module";
        } else if (ecu_id >= 0x7630 && ecu_id <= 0x763F) {
            return "Climate Control";
        } else {
            return "Unknown ECU";
        }
    }

    /**
     * Decode UDS/OBD-II service and return human-readable description
     */
    std::string decodeUDSService(const std::vector<uint8_t>& data) {
        if (data.empty()) return "Empty message";

        uint8_t service = data[0];
        std::stringstream result;

        // Check for negative response
        if (service == 0x7F && data.size() >= 3) {
            uint8_t requested_service = data[1];
            uint8_t nrc = data[2];
            result << "NEGATIVE RESPONSE - Service 0x" << std::hex << std::uppercase
                   << static_cast<int>(requested_service) << " failed with NRC 0x"
                   << static_cast<int>(nrc) << " (" << getNRCDescription(nrc) << ")";
            return result.str();
        }

        // Positive responses (service + 0x40)
        if (service >= 0x40 && service <= 0x7F) {
            uint8_t original_service = service - 0x40;
            result << "POSITIVE RESPONSE for ";
            result << getServiceDescription(original_service);

            if (data.size() > 1) {
                result << " - Data: ";
                for (size_t i = 1; i < data.size(); ++i) {
                    if (i > 1) result << " ";
                    result << std::setfill('0') << std::setw(2) << std::hex
                           << std::uppercase << static_cast<int>(data[i]);
                }
            }
            return result.str();
        }

        // Request services
        result << "REQUEST - " << getServiceDescription(service);

        // Add specific decoding based on service
        switch (service) {
            case 0x01: // Show current data
                if (data.size() > 1) {
                    result << " - PID 0x" << std::hex << std::uppercase
                           << static_cast<int>(data[1]);
                    if (data.size() > 2) {
                        result << " (" << getOBDPIDDescription(data[1]) << ")";
                    }
                }
                break;

            case 0x22: // Read data by identifier
                if (data.size() >= 3) {
                    uint16_t did = (data[1] << 8) | data[2];
                    result << " - DID 0x" << std::hex << std::uppercase << did;
                    result << " (" << getDIDDescription(did) << ")";
                }
                break;

            case 0x27: // Security access
                if (data.size() > 1) {
                    uint8_t level = data[1];
                    if (level % 2 == 1) {
                        result << " - Request seed for level " << std::dec << ((level + 1) / 2);
                    } else {
                        result << " - Send key for level " << std::dec << (level / 2);
                    }
                }
                break;

            case 0x34: // Request download
                result << " - Memory address and size specified";
                break;

            case 0x36: // Transfer data
                if (data.size() > 1) {
                    result << " - Block sequence: " << std::dec << static_cast<int>(data[1]);
                }
                break;

            default:
                if (data.size() > 1) {
                    result << " - Data: ";
                    for (size_t i = 1; i < data.size(); ++i) {
                        if (i > 1) result << " ";
                        result << std::setfill('0') << std::setw(2) << std::hex
                               << std::uppercase << static_cast<int>(data[i]);
                    }
                }
                break;
        }

        return result.str();
    }

    /**
     * Get service description
     */
    std::string getServiceDescription(uint8_t service) {
        switch (service) {
            case 0x01: return "Show current data (OBD-II)";
            case 0x02: return "Show freeze frame data";
            case 0x03: return "Show stored DTCs";
            case 0x04: return "Clear DTCs";
            case 0x05: return "Test results, oxygen sensor monitoring";
            case 0x06: return "Test results, other component/system monitoring";
            case 0x07: return "Show pending DTCs";
            case 0x08: return "Control operation of on-board component/system";
            case 0x09: return "Request vehicle information";
            case 0x0A: return "Permanent DTCs";
            case 0x10: return "Diagnostic session control";
            case 0x11: return "ECU reset";
            case 0x14: return "Clear diagnostic information";
            case 0x19: return "Read DTC information";
            case 0x22: return "Read data by identifier";
            case 0x23: return "Read memory by address";
            case 0x24: return "Read scaling data by identifier";
            case 0x27: return "Security access";
            case 0x28: return "Communication control";
            case 0x2A: return "Read data by periodic identifier";
            case 0x2C: return "Dynamically define data identifier";
            case 0x2E: return "Write data by identifier";
            case 0x2F: return "Input output control by identifier";
            case 0x31: return "Routine control";
            case 0x34: return "Request download";
            case 0x35: return "Request upload";
            case 0x36: return "Transfer data";
            case 0x37: return "Request transfer exit";
            case 0x38: return "Request file transfer";
            case 0x3D: return "Write memory by address";
            case 0x3E: return "Tester present";
            case 0x83: return "Access timing parameter";
            case 0x84: return "Secured data transmission";
            case 0x85: return "Control DTC setting";
            case 0x86: return "Response on event";
            case 0x87: return "Link control";
            default:
                std::stringstream ss;
                ss << "Unknown service 0x" << std::hex << std::uppercase << static_cast<int>(service);
                return ss.str();
        }
    }

    /**
     * Get NRC (Negative Response Code) description
     */
    std::string getNRCDescription(uint8_t nrc) {
        switch (nrc) {
            case 0x10: return "General reject";
            case 0x11: return "Service not supported";
            case 0x12: return "Sub-function not supported";
            case 0x13: return "Incorrect message length or invalid format";
            case 0x14: return "Response too long";
            case 0x21: return "Busy repeat request";
            case 0x22: return "Conditions not correct";
            case 0x24: return "Request sequence error";
            case 0x25: return "No response from subnet component";
            case 0x26: return "Failure prevents execution of requested action";
            case 0x31: return "Request out of range";
            case 0x33: return "Security access denied";
            case 0x35: return "Invalid key";
            case 0x36: return "Exceed number of attempts";
            case 0x37: return "Required time delay not expired";
            case 0x70: return "Upload download not accepted";
            case 0x71: return "Transfer data suspended";
            case 0x72: return "General programming failure";
            case 0x73: return "Wrong block sequence counter";
            case 0x78: return "Request correctly received - response pending";
            case 0x7E: return "Sub-function not supported in active session";
            case 0x7F: return "Service not supported in active session";
            default:
                std::stringstream ss;
                ss << "Unknown NRC 0x" << std::hex << std::uppercase << static_cast<int>(nrc);
                return ss.str();
        }
    }

    /**
     * Get OBD-II PID description
     */
    std::string getOBDPIDDescription(uint8_t pid) {
        switch (pid) {
            case 0x00: return "PIDs supported [01-20]";
            case 0x01: return "Monitor status since DTCs cleared";
            case 0x02: return "Freeze DTC";
            case 0x03: return "Fuel system status";
            case 0x04: return "Calculated engine load";
            case 0x05: return "Engine coolant temperature";
            case 0x06: return "Short term fuel trim—Bank 1";
            case 0x07: return "Long term fuel trim—Bank 1";
            case 0x08: return "Short term fuel trim—Bank 2";
            case 0x09: return "Long term fuel trim—Bank 2";
            case 0x0A: return "Fuel pressure";
            case 0x0B: return "Intake manifold absolute pressure";
            case 0x0C: return "Engine RPM";
            case 0x0D: return "Vehicle speed";
            case 0x0E: return "Timing advance";
            case 0x0F: return "Intake air temperature";
            case 0x10: return "MAF air flow rate";
            case 0x11: return "Throttle position";
            case 0x12: return "Commanded secondary air status";
            case 0x13: return "Oxygen sensors present";
            case 0x14: return "Oxygen Sensor 1 - Voltage and trim";
            case 0x15: return "Oxygen Sensor 2 - Voltage and trim";
            case 0x1C: return "OBD standards this vehicle conforms to";
            case 0x1F: return "Run time since engine start";
            case 0x20: return "PIDs supported [21-40]";
            case 0x21: return "Distance traveled with malfunction indicator lamp on";
            case 0x2F: return "Fuel Tank Level Input";
            case 0x33: return "Absolute Barometric Pressure";
            case 0x40: return "PIDs supported [41-60]";
            case 0x41: return "Monitor status this drive cycle";
            case 0x42: return "Control module voltage";
            case 0x43: return "Absolute load value";
            case 0x44: return "Fuel–Air commanded equivalence ratio";
            case 0x45: return "Relative throttle position";
            case 0x46: return "Ambient air temperature";
            case 0x47: return "Absolute throttle position B";
            case 0x49: return "Accelerator pedal position D";
            case 0x4A: return "Accelerator pedal position E";
            case 0x4C: return "Commanded throttle actuator";
            case 0x51: return "Fuel Type";
            case 0x52: return "Ethanol fuel %";
            default:
                std::stringstream ss;
                ss << "Unknown PID 0x" << std::hex << std::uppercase << static_cast<int>(pid);
                return ss.str();
        }
    }

    /**
     * Get DID (Data Identifier) description
     */
    std::string getDIDDescription(uint16_t did) {
        switch (did) {
            case 0xF010: return "Active Diagnostic Session";
            case 0xF011: return "ECU Serial Number";
            case 0xF012: return "ECU Manufacturing Date";
            case 0xF013: return "ECU Installation Date";
            case 0xF015: return "ECU Installation Time";
            case 0xF018: return "Application Software Identification";
            case 0xF019: return "Application Software Version Number";
            case 0xF01A: return "Application Software Fingerprint";
            case 0xF020: return "Application Data Identification";
            case 0xF021: return "Application Data Fingerprint";
            case 0xF030: return "Active Diagnostic Session Data Identifier";
            case 0xF040: return "Vehicle Manufacturer Specific";
            case 0xF050: return "Vehicle Manufacturer Specific";
            case 0xF086: return "Active Diagnostic Session Data Identifier";
            case 0xF090: return "Vehicle Identification Number";
            case 0xF0F0: return "Active Diagnostic Session Data Identifier";
            case 0xF186: return "Active Diagnostic Session Data Identifier";
            case 0xF187: return "Vehicle Manufacturer Spare Part Number";
            case 0xF188: return "Vehicle Manufacturer ECU Software Number";
            case 0xF189: return "Vehicle Manufacturer ECU Software Version Number";
            case 0xF18A: return "System Identification";
            case 0xF18B: return "ECU Manufacturing Date";
            case 0xF18C: return "ECU Serial Number";
            case 0xF190: return "Vehicle Identification Number";
            case 0xF191: return "Vehicle Manufacturer ECU Hardware Number";
            case 0xF192: return "Vehicle Manufacturer ECU Software Number";
            case 0xF193: return "Vehicle Manufacturer ECU Software Version Number";
            case 0xF194: return "System Identification";
            case 0xF195: return "ECU Manufacturing Date";
            case 0xF196: return "ECU Installation Date";
            case 0xF197: return "ECU Installation Time";
            case 0xF198: return "Vehicle Manufacturer ECU Hardware Version Number";
            case 0xF199: return "Vehicle Manufacturer ECU Software Calibration Identification";
            case 0xF19A: return "Vehicle Manufacturer ECU Software Calibration Verification Number";
            case 0xF19D: return "Vehicle Manufacturer ECU Installation Date";
            case 0xF19E: return "Vehicle Manufacturer ECU Installation Time";
            case 0xF1A0: return "Vehicle Manufacturer Specific";
            case 0xF1A1: return "Vehicle Manufacturer Specific";
            case 0xF1A2: return "Vehicle Manufacturer Specific";
            case 0xF1A3: return "Vehicle Manufacturer Specific";
            default:
                std::stringstream ss;
                ss << "Unknown DID 0x" << std::hex << std::uppercase << did;
                return ss.str();
        }
    }

    /**
     * Print assembled message in detailed format
     */
    void printDetailedMessage(uint16_t ecu_id, const std::vector<uint8_t>& data, bool is_complete = true) {
        std::cout << "ECU 0x" << std::hex << std::uppercase << ecu_id;

        if (is_complete) {
            std::cout << " [Complete, " << std::dec << data.size() << " bytes]:" << std::endl;
        } else {
            std::cout << " [Incomplete, " << std::dec << data.size() << " bytes]:" << std::endl;
        }

        // Hex with spaces
        std::cout << "  Hex: ";
        for (size_t i = 0; i < data.size(); ++i) {
            if (i > 0) std::cout << " ";
            std::cout << std::setfill('0') << std::setw(2) << std::hex
                      << static_cast<int>(data[i]);
        }
        std::cout << std::endl;

        // Decimal
        std::cout << "  Dec: ";
        for (size_t i = 0; i < data.size(); ++i) {
            if (i > 0) std::cout << "  ";
            std::cout << std::setfill(' ') << std::setw(2) << std::dec
                      << static_cast<int>(data[i]);
        }
        std::cout << std::endl;

        // ASCII
        std::cout << "  ASCII: ";
        for (size_t i = 0; i < data.size(); ++i) {
            if (i > 0) std::cout << " ";
            char c = static_cast<char>(data[i]);
            if (c >= 32 && c <= 126) {
                std::cout << "[" << c << "]";
            } else {
                std::cout << "[.]";
            }
        }
        std::cout << std::endl;

        // UDS Analysis if applicable
        if (data.size() > 0) {
            uint8_t service = data[0];
            std::cout << "  UDS: 0x" << std::hex << std::uppercase << static_cast<int>(service);

            if (service == 0x7F && data.size() >= 3) {
                std::cout << " (Negative Response)";
            } else if (service >= 0x40 && service <= 0x7F) {
                std::cout << " (Positive Response for 0x" << std::hex << (service - 0x40) << ")";
            } else if (service == 0x34) {
                std::cout << " (Request Download)";
            } else if (service == 0x22) {
                std::cout << " (Read Data By Identifier)";
            } else {
                std::cout << " (Service)";
            }
            std::cout << std::endl;
        }

        std::cout << std::endl;
    }
    
    /**
     * Parse input file and process all frames
     */
    void parseFile(const std::string& filename) {
        std::ifstream file(filename);
        if (!file.is_open()) {
            throw std::runtime_error("Cannot open file: " + filename);
        }
        
        std::string line;
        while (std::getline(file, line)) {
            // Skip empty lines
            if (line.empty()) continue;
            
            try {
                ISOTPFrame frame = parseFrame(line);
                processFrame(frame);
            } catch (const std::exception& e) {
                // Silently skip malformed lines
                // Uncomment next line if you want to see parsing errors:
                // std::cerr << "Error parsing line '" << line << "': " << e.what() << std::endl;
            }
        }
        
        file.close();
        
        // Print any remaining incomplete messages as they are
        for (const auto& pair : assembled_messages) {
            if (pair.second.size() > 0) {
                // Print incomplete message anyway
                if (decode_messages) {
                    printDecodedMessage(pair.first, pair.second);
                } else {
                    printMessage(pair.first, pair.second);
                }
                // Warning to stderr only (won't be captured in output file)
                // Comment out the next 3 lines if you don't want warnings at all
                // std::cerr << "Warning: Incomplete message for ECU " << std::hex
                //           << pair.first << " (expected " << expected_lengths[pair.first]
                //           << " bytes, got " << std::dec << pair.second.size() << ")" << std::endl;
            }
        }
    }
};

int main(int argc, char* argv[]) {
    std::string filename = "transcript.txt";
    bool detailed = false;
    bool decode = false;

    // Parse command line arguments
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        if (arg == "--detailed" || arg == "-d") {
            detailed = true;
        } else if (arg == "--decode" || arg == "-u") {
            decode = true;
        } else if (arg == "--help" || arg == "-h") {
            std::cout << "Usage: " << argv[0] << " [options] [filename]" << std::endl;
            std::cout << "Options:" << std::endl;
            std::cout << "  -d, --detailed    Show detailed analysis" << std::endl;
            std::cout << "  -u, --decode      Decode UDS/OBD-II messages" << std::endl;
            std::cout << "  -h, --help        Show this help" << std::endl;
            std::cout << "Default filename: transcript.txt" << std::endl;
            return 0;
        } else {
            filename = arg;
        }
    }

    try {
        CANMessageParser parser(decode);
        if (detailed) {
            std::cout << "=== DETAILED CAN MESSAGE ANALYSIS ===" << std::endl << std::endl;
        } else if (decode) {
            std::cout << "=== DECODED CAN MESSAGES ===" << std::endl << std::endl;
        }
        parser.parseFile(filename);
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
